"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'
import { 
  Download, 
  Upload, 
  Database, 
  FileJson, 
  AlertTriangle,
  CheckCircle,
  Loader2
} from 'lucide-react'

export default function DataManagementPage() {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [importType, setImportType] = useState('')
  const [exportType, setExportType] = useState('')
  const [importFile, setImportFile] = useState<File | null>(null)

  const handleImport = async () => {
    if (!importFile || !importType) {
      toast.error('Please select a file and import type')
      return
    }

    setImporting(true)

    try {
      const fileContent = await importFile.text()
      const data = JSON.parse(fileContent)

      const response = await fetch('/api/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: importType,
          data: Array.isArray(data) ? data : data.data || [data]
        }),
      })

      if (response.ok) {
        const result = await response.json()
        toast.success(result.message)
        if (result.errors.length > 0) {
          console.warn('Import errors:', result.errors)
          toast.warning(`${result.errors.length} items had errors. Check console for details.`)
        }
      } else {
        const error = await response.json()
        toast.error(error.error || 'Import failed')
      }
    } catch (error) {
      toast.error('Failed to parse or import file')
    } finally {
      setImporting(false)
      setImportFile(null)
    }
  }

  const handleExport = async () => {
    if (!exportType) {
      toast.error('Please select an export type')
      return
    }

    setExporting(true)

    try {
      const response = await fetch(`/api/export?type=${exportType}`)
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = response.headers.get('Content-Disposition')?.split('filename=')[1]?.replace(/"/g, '') || `${exportType}-export.json`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('Export completed successfully')
      } else {
        toast.error('Export failed')
      }
    } catch (error) {
      toast.error('Export failed')
    } finally {
      setExporting(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (file.type === 'application/json' || file.name.endsWith('.json')) {
        setImportFile(file)
      } else {
        toast.error('Please select a JSON file')
        e.target.value = ''
      }
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Data Management</h1>
        <p className="text-muted-foreground">
          Import and export your CMS data for backup and migration
        </p>
      </div>

      <Tabs defaultValue="export" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="export">Export Data</TabsTrigger>
          <TabsTrigger value="import">Import Data</TabsTrigger>
        </TabsList>

        <TabsContent value="export" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Export Data
              </CardTitle>
              <CardDescription>
                Download your CMS data as JSON files for backup or migration
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="export-type">Export Type</Label>
                <Select value={exportType} onValueChange={setExportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select what to export" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Complete Backup (All Data)</SelectItem>
                    <SelectItem value="blogs">Blog Posts</SelectItem>
                    <SelectItem value="services">Services</SelectItem>
                    <SelectItem value="packages">Packages</SelectItem>
                    <SelectItem value="testimonials">Testimonials</SelectItem>
                    <SelectItem value="gallery">Gallery</SelectItem>
                    <SelectItem value="settings">Site Settings</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button 
                onClick={handleExport} 
                disabled={!exportType || exporting}
                className="w-full"
              >
                {exporting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </>
                )}
              </Button>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-900">Export Information</p>
                    <ul className="mt-1 text-blue-700 space-y-1">
                      <li>• Exported data includes all metadata and relationships</li>
                      <li>• Files are in JSON format for easy processing</li>
                      <li>• Complete backup includes all content types</li>
                      <li>• Regular backups are recommended</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Import Data
              </CardTitle>
              <CardDescription>
                Upload JSON files to import data into your CMS
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="import-type">Import Type</Label>
                <Select value={importType} onValueChange={setImportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select what to import" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="blogs">Blog Posts</SelectItem>
                    <SelectItem value="services">Services</SelectItem>
                    <SelectItem value="packages">Packages</SelectItem>
                    <SelectItem value="testimonials">Testimonials</SelectItem>
                    <SelectItem value="gallery">Gallery</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="import-file">JSON File</Label>
                <Input
                  id="import-file"
                  type="file"
                  accept=".json,application/json"
                  onChange={handleFileChange}
                />
                {importFile && (
                  <p className="text-sm text-muted-foreground">
                    Selected: {importFile.name} ({(importFile.size / 1024).toFixed(1)} KB)
                  </p>
                )}
              </div>

              <Button 
                onClick={handleImport} 
                disabled={!importFile || !importType || importing}
                className="w-full"
              >
                {importing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Import Data
                  </>
                )}
              </Button>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-amber-900">Import Guidelines</p>
                    <ul className="mt-1 text-amber-700 space-y-1">
                      <li>• Only JSON files are supported</li>
                      <li>• Existing items with same titles/slugs will be skipped</li>
                      <li>• Large files may take time to process</li>
                      <li>• Always backup your data before importing</li>
                      <li>• Check console for detailed error messages</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileJson className="h-5 w-5" />
                Expected JSON Format
              </CardTitle>
              <CardDescription>
                Examples of the expected JSON structure for imports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Blog Posts</h4>
                  <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
{`[
  {
    "title": "Summer Makeup Trends",
    "content": "Content here...",
    "author": "Anjali",
    "excerpt": "Brief description",
    "featured": true,
    "image": "https://example.com/image.jpg"
  }
]`}
                  </pre>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Services</h4>
                  <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
{`[
  {
    "title": "Bridal Makeup",
    "description": "Complete bridal makeup service",
    "features": ["Pre-bridal consultation", "Trial session"],
    "price": "NPR 15,000",
    "duration": "3-4 hours",
    "category": "Bridal"
  }
]`}
                  </pre>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Packages</h4>
                  <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
{`[
  {
    "name": "Bridal Complete Package",
    "description": "Complete bridal package",
    "services": ["Bridal Makeup", "Hair Styling"],
    "features": ["Trial session", "Touch-up kit"],
    "price": "NPR 25,000",
    "originalPrice": "NPR 30,000",
    "category": "Bridal"
  }
]`}
                  </pre>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Testimonials</h4>
                  <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
{`[
  {
    "name": "Sarah Johnson",
    "message": "Amazing makeup artist!",
    "rating": 5,
    "service": "Bridal Makeup",
    "email": "<EMAIL>"
  }
]`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
