"use client"

import { useState } from 'react'
import { ImageUpload } from '@/components/ui/image-upload'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export function TestUpload() {
  const [imageUrl, setImageUrl] = useState('')

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Test Image Upload</CardTitle>
        <CardDescription>
          Test the basic Cloudinary upload functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <ImageUpload
          value={imageUrl}
          onChange={setImageUrl}
          folder="test"
          label="Test Image"
        />
        
        {imageUrl && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Upload successful!</p>
            <p className="text-xs text-gray-500 break-all">
              URL: {imageUrl}
            </p>
          </div>
        )}
        
        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Instructions:</strong></p>
          <ul className="list-disc list-inside space-y-1">
            <li>Click &ldquo;Upload Image&rdquo; to select a file</li>
            <li>Choose an image (JPG, PNG, GIF, WebP)</li>
            <li>Watch the progress bar during upload</li>
            <li>See the preview when complete</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
