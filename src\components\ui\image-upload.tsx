"use client"

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react'
import Image from 'next/image'

interface ImageUploadProps {
  value?: string
  onChange: (url: string) => void
  onRemove?: () => void
  disabled?: boolean
  folder?: string
  label?: string
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled,
  folder = 'anjali-cms',
  label = 'Image'
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Cloudinary configuration
  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME

  const uploadToCloudinary = async (file: File) => {
    if (!cloudName) {
      throw new Error('Cloudinary cloud name not configured')
    }

    // Get signature from our API
    const signatureResponse = await fetch('/api/cloudinary-signature', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ folder }),
    })

    if (!signatureResponse.ok) {
      throw new Error('Failed to get upload signature')
    }

    const signatureData = await signatureResponse.json()

    // Log for debugging (remove in production)
    console.log('Signature data received:', signatureData)

    // Prepare form data for signed upload (minimal parameters to match signature)
    const formData = new FormData()
    formData.append('file', file)
    formData.append('signature', signatureData.signature)
    formData.append('timestamp', signatureData.timestamp.toString())
    formData.append('api_key', signatureData.api_key)
    formData.append('folder', folder)

    // Log the form data for debugging
    console.log('Upload parameters:')
    for (const [key, value] of formData.entries()) {
      if (key !== 'file') {
        console.log(`${key}: ${value}`)
      }
    }

    // Upload to Cloudinary
    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
      {
        method: 'POST',
        body: formData,
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error?.message || 'Upload failed')
    }

    return response.json()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setError('Please select a valid image file (JPG, PNG, GIF, WebP)')
      return
    }

    // Validate file size (5MB)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      setError('File size must be less than 5MB')
      return
    }

    setIsUploading(true)
    setError(null)
    setUploadProgress(0)

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      const result = await uploadToCloudinary(file)

      clearInterval(progressInterval)
      setUploadProgress(100)

      // Small delay to show 100% progress
      setTimeout(() => {
        onChange(result.secure_url)
        setIsUploading(false)
        setUploadProgress(0)
      }, 500)

    } catch (error) {
      setIsUploading(false)
      setUploadProgress(0)
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      setError(errorMessage)
      console.error('Upload error:', error)
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleRemove = () => {
    if (onRemove) {
      onRemove()
    } else {
      onChange('')
    }
    setError(null)
  }

  return (
    <div className="space-y-4">
      <Label>{label}</Label>

      {/* Current Image Display */}
      {value && (
        <div className="relative w-32 h-32 rounded-lg overflow-hidden border">
          <Image
            src={value}
            alt="Uploaded image"
            fill
            className="object-cover"
          />
          <button
            type="button"
            onClick={handleRemove}
            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
            disabled={disabled || isUploading}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Upload Button */}
      <div className="space-y-2">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          disabled={disabled || isUploading}
          className="hidden"
        />

        <Button
          type="button"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || isUploading}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Uploading... {uploadProgress}%
            </>
          ) : (
            <>
              <Upload className="w-4 h-4 mr-2" />
              {value ? 'Change Image' : 'Upload Image'}
            </>
          )}
        </Button>

        {/* Progress Bar */}
        {isUploading && (
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        )}

        {/* Error Message */}
        {error && (
          <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
            {error}
          </p>
        )}
      </div>

      {/* Instructions */}
      <div className="text-sm text-gray-500 space-y-1">
        <p><strong>Supported formats:</strong> JPG, PNG, GIF, WebP</p>
        <p><strong>Max file size:</strong> 5MB</p>
        <p><strong>Upload method:</strong> Signed upload (no preset required)</p>
        {!cloudName && (
          <p className="text-red-600">
            ⚠️ Cloudinary not configured. Please set up environment variables.
          </p>
        )}
      </div>
    </div>
  )
}


