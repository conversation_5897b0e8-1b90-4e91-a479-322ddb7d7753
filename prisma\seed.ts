import { PrismaClient, ServiceStatus, PackageStatus, GalleryStatus, TestimonialStatus, BlogStatus, ConfigType } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create admin user
  console.log('👤 Creating admin user...')
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON> Admin',
      password: hashedPassword,
      role: 'ADMIN',
    },
  })
  console.log('✅ Admin user created:', admin.email)

  // Create categories
  console.log('📂 Creating categories...')
  const bridalCategory = await prisma.category.upsert({
    where: { slug: 'bridal' },
    update: {},
    create: {
      name: '<PERSON>rida<PERSON>',
      slug: 'bridal',
      description: 'Bridal makeup and styling services',
    },
  })

  const partyCategory = await prisma.category.upsert({
    where: { slug: 'party' },
    update: {},
    create: {
      name: 'Party',
      slug: 'party',
      description: 'Party and event makeup services',
    },
  })

  const traditionalCategory = await prisma.category.upsert({
    where: { slug: 'traditional' },
    update: {},
    create: {
      name: 'Traditional',
      slug: 'traditional',
      description: 'Traditional and cultural makeup',
    },
  })

  // Create tags
  console.log('🏷️ Creating tags...')
  const tags = [
    { name: 'Natural Look', slug: 'natural-look' },
    { name: 'Bold Makeup', slug: 'bold-makeup' },
    { name: 'Smokey Eyes', slug: 'smokey-eyes' },
    { name: 'Glam', slug: 'glam' },
    { name: 'Minimalist', slug: 'minimalist' },
  ]

  const createdTags = []
  for (const tag of tags) {
    const createdTag = await prisma.tag.upsert({
      where: { slug: tag.slug },
      update: {},
      create: tag,
    })
    createdTags.push(createdTag)
  }

  // Create services
  console.log('💄 Creating services...')
  const services = [
    {
      title: 'Bridal Makeup',
      slug: 'bridal-makeup',
      description: 'Complete bridal makeup service for your special day. Includes pre-bridal consultation, trial session, and wedding day makeup application.',
      features: [
        'Pre-bridal consultation',
        'Trial makeup session',
        'Wedding day makeup',
        'Touch-up kit included',
        'Hair styling available',
      ],
      duration: '3-4 hours',
      price: 'Starting from NPR 15,000',
      category: 'Bridal',
      popular: true,
      status: ServiceStatus.ACTIVE,
    },
    {
      title: 'Party Makeup',
      slug: 'party-makeup',
      description: 'Glamorous makeup for parties, events, and special occasions. Perfect for making you look stunning at any celebration.',
      features: [
        'Event-specific makeup',
        'Long-lasting formula',
        'Photo-ready finish',
        'Complimentary touch-ups',
      ],
      duration: '1-2 hours',
      price: 'Starting from NPR 8,000',
      category: 'Party',
      popular: true,
      status: ServiceStatus.ACTIVE,
    },
    {
      title: 'Traditional Makeup',
      slug: 'traditional-makeup',
      description: 'Authentic traditional makeup for cultural events, festivals, and ceremonies.',
      features: [
        'Cultural authenticity',
        'Traditional techniques',
        'Appropriate color palette',
        'Respectful styling',
      ],
      duration: '2-3 hours',
      price: 'Starting from NPR 10,000',
      category: 'Traditional',
      status: ServiceStatus.ACTIVE,
    },
    {
      title: 'Photoshoot Makeup',
      slug: 'photoshoot-makeup',
      description: 'Professional makeup designed specifically for photography and videography sessions.',
      features: [
        'Camera-ready finish',
        'HD makeup techniques',
        'Multiple look options',
        'Quick touch-ups',
      ],
      duration: '2-3 hours',
      price: 'Starting from NPR 12,000',
      category: 'Professional',
      status: ServiceStatus.ACTIVE,
    },
  ]

  for (const service of services) {
    await prisma.service.upsert({
      where: { slug: service.slug },
      update: {},
      create: service,
    })
  }

  // Create packages
  console.log('📦 Creating packages...')
  const packages = [
    {
      name: 'Bridal Complete Package',
      slug: 'bridal-complete-package',
      description: 'Complete bridal package including all pre-wedding and wedding day services.',
      services: ['Pre-bridal consultation', 'Trial makeup session', 'Wedding day makeup', 'Hair styling', 'Saree draping'],
      features: [
        'Multiple trial sessions',
        'Wedding day makeup and hair',
        'Saree draping included',
        'Touch-up kit',
        'Photography coordination',
        'Family makeup discount',
      ],
      price: 'NPR 25,000',
      originalPrice: 'NPR 30,000',
      duration: '6-8 hours',
      category: 'Bridal',
      popular: true,
      status: PackageStatus.ACTIVE,
    },
    {
      name: 'Party Glam Package',
      slug: 'party-glam-package',
      description: 'Perfect package for parties and special events with glamorous styling.',
      services: ['Party makeup', 'Hair styling', 'Outfit consultation'],
      features: [
        'Glamorous makeup',
        'Professional hair styling',
        'Outfit coordination advice',
        'Touch-up products',
        'Photo session tips',
      ],
      price: 'NPR 15,000',
      duration: '3-4 hours',
      category: 'Party',
      popular: true,
      status: PackageStatus.ACTIVE,
    },
    {
      name: 'Photoshoot Pro Package',
      slug: 'photoshoot-pro-package',
      description: 'Professional package designed for photoshoots and commercial work.',
      services: ['HD makeup', 'Hair styling', 'Multiple looks', 'Touch-ups'],
      features: [
        'HD makeup techniques',
        'Multiple look changes',
        'Professional hair styling',
        'Continuous touch-ups',
        'Photographer coordination',
      ],
      price: 'NPR 20,000',
      duration: '4-6 hours',
      category: 'Professional',
      status: PackageStatus.ACTIVE,
    },
  ]

  for (const pkg of packages) {
    await prisma.package.upsert({
      where: { slug: pkg.slug },
      update: {},
      create: pkg,
    })
  }

  // Create testimonials
  console.log('💬 Creating testimonials...')
  const testimonials = [
    {
      name: 'Priya Sharma',
      email: '<EMAIL>',
      message: 'Anjali did an amazing job on my wedding day! The makeup was flawless and lasted the entire day. I felt like a princess. Highly recommended for all brides!',
      rating: 5,
      service: 'Bridal Makeup',
      status: TestimonialStatus.APPROVED,
    },
    {
      name: 'Sita Rai',
      message: 'Perfect makeup for my engagement party. Anjali understood exactly what I wanted and delivered beyond my expectations. The team is professional and talented.',
      rating: 5,
      service: 'Party Makeup',
      status: TestimonialStatus.APPROVED,
    },
    {
      name: 'Kamala Thapa',
      email: '<EMAIL>',
      message: 'I had my makeup done for a traditional ceremony and it was absolutely beautiful. Anjali respects cultural traditions while adding her modern touch.',
      rating: 5,
      service: 'Traditional Makeup',
      status: TestimonialStatus.APPROVED,
    },
    {
      name: 'Rina Gurung',
      message: 'Amazing experience! The makeup was perfect for my photoshoot. Anjali is very professional and knows how to make you look your best on camera.',
      rating: 5,
      service: 'Photoshoot Makeup',
      status: TestimonialStatus.APPROVED,
    },
    {
      name: 'Maya Shrestha',
      email: '<EMAIL>',
      message: 'Excellent service and beautiful results. Anjali is not just talented but also very friendly and makes you feel comfortable throughout the process.',
      rating: 5,
      service: 'Bridal Makeup',
      status: TestimonialStatus.APPROVED,
    },
  ]

  for (const testimonial of testimonials) {
    await prisma.testimonial.create({
      data: testimonial,
    })
  }

  // Create blog posts
  console.log('📝 Creating blog posts...')
  const blogPosts = [
    {
      title: 'Top 10 Bridal Makeup Trends for 2024',
      slug: 'top-10-bridal-makeup-trends-2024',
      excerpt: 'Discover the latest bridal makeup trends that will make you look stunning on your wedding day.',
      content: '<p>Your wedding day is one of the most important days of your life, and you want to look absolutely perfect. Here are the top 10 bridal makeup trends for 2024 that will help you achieve that flawless, radiant look.</p><h2>1. Natural Glowing Skin</h2><p>The trend towards natural, dewy skin continues to dominate bridal makeup. Focus on achieving a healthy, radiant complexion that looks like your skin but better.</p><h2>2. Soft Smokey Eyes</h2><p>Classic smokey eyes get a softer, more romantic update with neutral tones and subtle blending techniques.</p>',
      author: 'Anjali',
      status: BlogStatus.PUBLISHED,
      featured: true,
      categoryId: bridalCategory.id,
      publishedAt: new Date(),
    },
    {
      title: 'How to Choose the Perfect Makeup for Your Skin Tone',
      slug: 'choose-perfect-makeup-skin-tone',
      excerpt: 'Learn how to select makeup colors that complement your unique skin tone for the most flattering results.',
      content: '<p>Choosing the right makeup colors for your skin tone can make all the difference in how your makeup looks. Here\'s a comprehensive guide to help you find your perfect shades.</p><h2>Understanding Undertones</h2><p>The key to finding your perfect makeup match lies in understanding your skin\'s undertones...</p>',
      author: 'Anjali',
      status: BlogStatus.PUBLISHED,
      categoryId: partyCategory.id,
      publishedAt: new Date(),
    },
    {
      title: 'Traditional Nepali Makeup: Honoring Culture with Beauty',
      slug: 'traditional-nepali-makeup-culture-beauty',
      excerpt: 'Explore the rich tradition of Nepali makeup and how to incorporate cultural elements into modern beauty.',
      content: '<p>Traditional Nepali makeup is a beautiful art form that reflects our rich cultural heritage. In this post, we explore how to honor these traditions while creating stunning modern looks.</p>',
      author: 'Anjali',
      status: BlogStatus.PUBLISHED,
      categoryId: traditionalCategory.id,
      publishedAt: new Date(),
    },
  ]

  for (const post of blogPosts) {
    await prisma.blog.upsert({
      where: { slug: post.slug },
      update: {},
      create: {
        ...post,
        tags: {
          create: [
            { tag: { connect: { id: createdTags[0].id } } },
            { tag: { connect: { id: createdTags[1].id } } },
          ]
        }
      },
    })
  }

  // Create site settings
  console.log('⚙️ Creating site settings...')
  const settings = [
    { key: 'site.name', value: 'Anjali Makeup Artist', type: ConfigType.TEXT, description: 'Site name' },
    { key: 'site.tagline', value: 'Enhancing Natural Beauty', type: ConfigType.TEXT, description: 'Site tagline' },
    { key: 'site.description', value: 'Professional makeup artist specializing in bridal, party, and traditional makeup services in Nepal.', type: ConfigType.TEXT, description: 'Site description' },
    { key: 'contact.email', value: '<EMAIL>', type: ConfigType.EMAIL, description: 'Contact email' },
    { key: 'contact.phone', value: '+977 98XXXXXXXX', type: ConfigType.TEXT, description: 'Contact phone' },
    { key: 'contact.address', value: 'Kathmandu, Nepal', type: ConfigType.TEXT, description: 'Business address' },
    { key: 'social.instagram', value: 'https://instagram.com/anjali_makeup', type: ConfigType.URL, description: 'Instagram URL' },
    { key: 'social.facebook', value: 'https://facebook.com/anjali.makeup.artist', type: ConfigType.URL, description: 'Facebook URL' },
    { key: 'business.hours', value: 'Monday - Saturday: 9:00 AM - 6:00 PM\nSunday: By appointment only', type: ConfigType.TEXT, description: 'Business hours' },
    { key: 'seo.meta_title', value: 'Anjali Makeup Artist - Professional Makeup Services in Nepal', type: ConfigType.TEXT, description: 'Default meta title' },
    { key: 'seo.meta_description', value: 'Professional makeup artist in Nepal specializing in bridal, party, and traditional makeup. Book your appointment for flawless, long-lasting makeup.', type: ConfigType.TEXT, description: 'Default meta description' },
  ]

  for (const setting of settings) {
    await prisma.siteConfig.upsert({
      where: { key: setting.key },
      update: {},
      create: setting,
    })
  }

  console.log('🎉 Database seeded successfully!')
  console.log('📧 Admin login: <EMAIL>')
  console.log('🔑 Admin password: admin123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
