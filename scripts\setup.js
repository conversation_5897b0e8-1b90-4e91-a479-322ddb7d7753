#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Anjali CMS...\n');

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file...');
  
  const envTemplate = `# Database
DATABASE_URL="postgresql://username:password@localhost:5432/anjali_cms"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# Cloudinary
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Optional: Email configuration
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"
`;

  fs.writeFileSync(envPath, envTemplate);
  console.log('✅ .env file created! Please update it with your actual values.\n');
} else {
  console.log('✅ .env file already exists.\n');
}

// Install dependencies
console.log('📦 Installing dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed successfully.\n');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Generate Prisma client
console.log('🔧 Generating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated successfully.\n');
} catch (error) {
  console.error('❌ Failed to generate Prisma client:', error.message);
  console.log('ℹ️  Please make sure your DATABASE_URL is configured in .env\n');
}

console.log('🎉 Setup complete!\n');
console.log('📋 Next steps:');
console.log('1. Update your .env file with actual database and Cloudinary credentials');
console.log('2. Run: npm run db:push (to create database tables)');
console.log('3. Run: npm run db:seed (to populate with sample data)');
console.log('4. Run: npm run dev (to start the development server)');
console.log('\n📧 Default admin login: <EMAIL>');
console.log('🔑 Default admin password: admin123');
console.log('\n🌟 Happy coding!');
