import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { corsResponse, corsOptionsResponse } from '@/lib/cors'

export async function OPTIONS() {
  return corsOptionsResponse()
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Public endpoint - no authentication required
    const { slug } = await params
    const packageData = await prisma.package.findUnique({
      where: {
        slug,
        status: 'ACTIVE' // Only return active packages
      }
    })

    if (!packageData) {
      return corsResponse({ error: 'Package not found' }, { status: 404 })
    }

    return corsResponse(packageData)
  } catch (error) {
    console.error('Error fetching package by slug:', error)
    return corsResponse({ error: 'Internal server error' }, { status: 500 })
  }
}
