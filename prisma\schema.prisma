// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(ADMIN)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum UserRole {
  ADMIN
  EDITOR
}

// Blog models
model Blog {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  excerpt     String?
  content     String
  author      String
  publishedAt DateTime?
  updatedAt   DateTime @updatedAt
  createdAt   DateTime @default(now())
  featured    <PERSON><PERSON><PERSON>  @default(false)
  image       String?
  readTime    String?
  status      BlogStatus @default(DRAFT)

  // SEO fields
  metaTitle       String?
  metaDescription String?
  keywords        String[]

  // Relations
  category     Category?     @relation(fields: [categoryId], references: [id])
  categoryId   String?
  tags         BlogTag[]

  @@map("blogs")
}

enum BlogStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  blogs Blog[]

  @@map("categories")
}

model Tag {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  blogs BlogTag[]

  @@map("tags")
}

model BlogTag {
  id     String @id @default(cuid())
  blogId String
  tagId  String

  // Relations
  blog Blog @relation(fields: [blogId], references: [id], onDelete: Cascade)
  tag  Tag  @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([blogId, tagId])
  @@map("blog_tags")
}

// Service models
model Service {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  description String
  features    String[]
  duration    String?
  price       String?
  image       String?
  category    String?
  popular     Boolean  @default(false)
  status      ServiceStatus @default(ACTIVE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("services")
}

enum ServiceStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}

// Testimonial models
model Testimonial {
  id        String   @id @default(cuid())
  name      String
  email     String?
  message   String
  rating    Int      @default(5)
  image     String?
  service   String?
  status    TestimonialStatus @default(PENDING)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("testimonials")
}

enum TestimonialStatus {
  PENDING
  APPROVED
  REJECTED
}

// Gallery models
model Gallery {
  id          String   @id @default(cuid())
  title       String
  description String?
  image       String
  category    String?
  tags        String[]
  featured    Boolean  @default(false)
  status      GalleryStatus @default(ACTIVE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("gallery")
}

enum GalleryStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}

// Site Configuration model
model SiteConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  type        ConfigType @default(TEXT)
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("site_config")
}

enum ConfigType {
  TEXT
  JSON
  BOOLEAN
  NUMBER
  URL
  EMAIL
}

// Package models
model Package {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String
  services    String[] // Array of service names/IDs included
  features    String[] // Array of package features
  price       String
  originalPrice String? // For showing discounts
  duration    String?
  popular     Boolean  @default(false)
  image       String?
  category    String?
  status      PackageStatus @default(ACTIVE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("packages")
}

enum PackageStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}
