#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// API routes that should be public (no authentication required for GET)
const publicRoutes = [
  'src/app/api/packages/route.ts',
  'src/app/api/testimonials/route.ts', 
  'src/app/api/gallery/route.ts',
  'src/app/api/settings/route.ts'
];

const corsImport = `import { corsResponse, corsOptionsResponse } from '@/lib/cors'`;

const optionsHandler = `
export async function OPTIONS() {
  return corsOptionsResponse()
}
`;

function updateApiRoute(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Add CORS import if not present
  if (!content.includes('corsResponse')) {
    content = content.replace(
      /import { z } from 'zod'/,
      `import { z } from 'zod'\n${corsImport}`
    );
  }

  // Add OPTIONS handler if not present
  if (!content.includes('export async function OPTIONS')) {
    content = content.replace(
      /export async function GET/,
      `${optionsHandler}\nexport async function GET`
    );
  }

  // Remove authentication check for GET requests
  content = content.replace(
    /export async function GET\(request: NextRequest\) \{\s*try \{\s*const session = await getServerSession\(authOptions\)\s*if \(!session\) \{\s*return NextResponse\.json\(\{ error: 'Unauthorized' \}, \{ status: 401 \}\)\s*\}/,
    'export async function GET(request: NextRequest) {\n  try {\n    // Public endpoint - no authentication required'
  );

  // Update NextResponse.json to corsResponse
  content = content.replace(
    /return NextResponse\.json\(\{([^}]+)\}\)/g,
    'return corsResponse({$1})'
  );

  fs.writeFileSync(filePath, content);
  console.log(`Updated: ${filePath}`);
}

// Update all public routes
publicRoutes.forEach(route => {
  updateApiRoute(route);
});

console.log('✅ All API routes updated for public access with CORS support');
