import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { corsResponse, corsOptionsResponse } from '@/lib/cors'

const testimonialSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Valid email is required').optional(),
  message: z.string().min(1, 'Message is required'),
  rating: z.number().min(1).max(5).default(5),
  image: z.string().optional(),
  service: z.string().optional(),
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED']).default('PENDING'),
})

export async function OPTIONS() {
  return corsOptionsResponse()
}

export async function GET(request: NextRequest) {
  try {
    // Public endpoint - no authentication required

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const service = searchParams.get('service') || ''

    const skip = (page - 1) * limit

    const where: {
      OR?: Array<{
        name?: { contains: string; mode: 'insensitive' }
        message?: { contains: string; mode: 'insensitive' }
        service?: { contains: string; mode: 'insensitive' }
      }>
      status?: 'PENDING' | 'APPROVED' | 'REJECTED'
      rating?: number
      service?: { contains: string; mode: 'insensitive' }
    } = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { message: { contains: search, mode: 'insensitive' } },
        { service: { contains: search, mode: 'insensitive' } },
      ]
    }
    
    if (status && (status === 'PENDING' || status === 'APPROVED' || status === 'REJECTED')) {
      where.status = status
    }
    
    if (service) {
      where.service = { contains: service, mode: 'insensitive' }
    }

    const [testimonials, total] = await Promise.all([
      prisma.testimonial.findMany({
        where,
        orderBy: [
          { status: 'asc' }, // Pending first
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      prisma.testimonial.count({ where }),
    ])

    return corsResponse({
      testimonials,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching testimonials:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = testimonialSchema.parse(body)

    const testimonial = await prisma.testimonial.create({
      data: validatedData,
    })

    return NextResponse.json(testimonial, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues }, { status: 400 })
    }
    
    console.error('Error creating testimonial:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
