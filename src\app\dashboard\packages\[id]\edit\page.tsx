"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { PackageForm } from '@/components/forms/package-form'
import { toast } from 'sonner'

interface PackageData {
  id: string
  name: string
  description: string
  services: string[]
  features: string[]
  price: string
  originalPrice?: string
  duration?: string
  popular: boolean
  image?: string
  category?: string
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
}

export default function EditPackagePage() {
  const params = useParams()
  const [packageData, setPackageData] = useState<PackageData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPackage = async () => {
      try {
        const response = await fetch(`/api/packages/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setPackageData(data)
        } else {
          toast.error('Package not found')
        }
      } catch (error) {
        toast.error('Error fetching package')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchPackage()
    }
  }, [params.id])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!packageData) {
    return <div>Package not found</div>
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Edit Package</h1>
        <p className="text-muted-foreground">
          Update your package details and settings
        </p>
      </div>

      <PackageForm initialData={{
        ...packageData,
        services: packageData.services.map(service => ({ value: service })),
        features: packageData.features.map(feature => ({ value: feature }))
      }} isEditing />
    </div>
  )
}
