"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { BlogForm } from '@/components/forms/blog-form'
import { toast } from 'sonner'

interface Blog {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  author: string
  featured: boolean
  image?: string
  readTime?: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  metaTitle?: string
  metaDescription?: string
  keywords: string[]
  categoryId?: string
  tags: Array<{
    tag: {
      id: string
      name: string
    }
  }>
}

export default function EditBlogPage() {
  const params = useParams()
  const [blog, setBlog] = useState<Blog | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        const response = await fetch(`/api/blogs/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setBlog(data)
        } else {
          toast.error('Blog not found')
        }
      } catch (error) {
        toast.error('Error fetching blog')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchBlog()
    }
  }, [params.id])

  if (loading) {
    return <div>Loading...</div>
  }

  if (!blog) {
    return <div>Blog not found</div>
  }

  const initialData = {
    ...blog,
    keywords: blog.keywords.join(', '),
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Edit Blog Post</h1>
        <p className="text-muted-foreground">
          Update your blog post content and settings
        </p>
      </div>

      <BlogForm initialData={initialData} isEditing />
    </div>
  )
}
