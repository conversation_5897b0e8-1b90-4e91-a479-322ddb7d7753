import { NextResponse } from 'next/server'
import { v2 as cloudinary } from 'cloudinary'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

export async function GET() {
  try {
    // Test configuration
    const config = {
      cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET ? 'Set' : 'Missing',
    }

    // Test signature generation
    const timestamp = Math.round(new Date().getTime() / 1000)
    const params = {
      timestamp,
      folder: 'test',
    }

    const signature = cloudinary.utils.api_sign_request(
      params,
      process.env.CLOUDINARY_API_SECRET!
    )

    // Test string that would be signed
    const stringToSign = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key as keyof typeof params]}`)
      .join('&')

    return NextResponse.json({
      status: 'ok',
      config,
      test_signature: {
        params,
        signature,
        string_to_sign: stringToSign,
        timestamp,
      },
      instructions: [
        '1. Check that all config values are set (not "Missing")',
        '2. Use the exact same params in your upload request',
        '3. Make sure timestamp matches between signature and upload',
        '4. Parameters must be in the same order',
      ]
    })
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      config: {
        cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'Missing',
        api_key: process.env.CLOUDINARY_API_KEY ? 'Set' : 'Missing',
        api_secret: process.env.CLOUDINARY_API_SECRET ? 'Set' : 'Missing',
      }
    }, { status: 500 })
  }
}
