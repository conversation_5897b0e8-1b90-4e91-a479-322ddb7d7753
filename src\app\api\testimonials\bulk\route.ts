import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const bulkActionSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one testimonial must be selected'),
  action: z.enum(['approve', 'reject', 'delete']),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { ids, action } = bulkActionSchema.parse(body)

    let result
    let message

    switch (action) {
      case 'approve':
        result = await prisma.testimonial.updateMany({
          where: { id: { in: ids } },
          data: { status: 'APPROVED' },
        })
        message = `${result.count} testimonial(s) approved successfully`
        break

      case 'reject':
        result = await prisma.testimonial.updateMany({
          where: { id: { in: ids } },
          data: { status: 'REJECTED' },
        })
        message = `${result.count} testimonial(s) rejected successfully`
        break

      case 'delete':
        result = await prisma.testimonial.deleteMany({
          where: { id: { in: ids } },
        })
        message = `${result.count} testimonial(s) deleted successfully`
        break

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    return NextResponse.json({ 
      message,
      count: result.count 
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues }, { status: 400 })
    }
    
    console.error('Error performing bulk action:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
