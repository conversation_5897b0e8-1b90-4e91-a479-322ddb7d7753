import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const format = searchParams.get('format') || 'json'

    if (!type) {
      return NextResponse.json({ error: 'Export type is required' }, { status: 400 })
    }

    let data
    let filename

    switch (type) {
      case 'blogs':
        data = await exportBlogs()
        filename = `blogs-export-${new Date().toISOString().split('T')[0]}.json`
        break

      case 'services':
        data = await exportServices()
        filename = `services-export-${new Date().toISOString().split('T')[0]}.json`
        break

      case 'packages':
        data = await exportPackages()
        filename = `packages-export-${new Date().toISOString().split('T')[0]}.json`
        break

      case 'testimonials':
        data = await exportTestimonials()
        filename = `testimonials-export-${new Date().toISOString().split('T')[0]}.json`
        break

      case 'gallery':
        data = await exportGallery()
        filename = `gallery-export-${new Date().toISOString().split('T')[0]}.json`
        break

      case 'settings':
        data = await exportSettings()
        filename = `settings-export-${new Date().toISOString().split('T')[0]}.json`
        break

      case 'all':
        data = await exportAll()
        filename = `full-backup-${new Date().toISOString().split('T')[0]}.json`
        break

      default:
        return NextResponse.json({ error: 'Invalid export type' }, { status: 400 })
    }

    const response = new NextResponse(JSON.stringify(data, null, 2))
    response.headers.set('Content-Type', 'application/json')
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`)
    
    return response
  } catch (error) {
    console.error('Export error:', error)
    return NextResponse.json({ error: 'Export failed' }, { status: 500 })
  }
}

async function exportBlogs() {
  const blogs = await prisma.blog.findMany({
    include: {
      category: true,
      tags: {
        include: {
          tag: true
        }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  return {
    type: 'blogs',
    exportDate: new Date().toISOString(),
    count: blogs.length,
    data: blogs.map(blog => ({
      id: blog.id,
      title: blog.title,
      slug: blog.slug,
      excerpt: blog.excerpt,
      content: blog.content,
      author: blog.author,
      status: blog.status,
      featured: blog.featured,
      image: blog.image,
      readTime: blog.readTime,
      metaTitle: blog.metaTitle,
      metaDescription: blog.metaDescription,
      keywords: blog.keywords,
      category: blog.category?.name,
      tags: blog.tags.map(t => t.tag.name),
      publishedAt: blog.publishedAt,
      createdAt: blog.createdAt,
      updatedAt: blog.updatedAt
    }))
  }
}

async function exportServices() {
  const services = await prisma.service.findMany({
    orderBy: { createdAt: 'desc' }
  })

  return {
    type: 'services',
    exportDate: new Date().toISOString(),
    count: services.length,
    data: services
  }
}

async function exportTestimonials() {
  const testimonials = await prisma.testimonial.findMany({
    orderBy: { createdAt: 'desc' }
  })

  return {
    type: 'testimonials',
    exportDate: new Date().toISOString(),
    count: testimonials.length,
    data: testimonials
  }
}

async function exportGallery() {
  const gallery = await prisma.gallery.findMany({
    orderBy: { createdAt: 'desc' }
  })

  return {
    type: 'gallery',
    exportDate: new Date().toISOString(),
    count: gallery.length,
    data: gallery
  }
}

async function exportSettings() {
  const settings = await prisma.siteConfig.findMany({
    orderBy: { key: 'asc' }
  })

  return {
    type: 'settings',
    exportDate: new Date().toISOString(),
    count: settings.length,
    data: settings
  }
}

async function exportPackages() {
  const packages = await prisma.package.findMany({
    orderBy: { createdAt: 'desc' }
  })

  return {
    type: 'packages',
    exportDate: new Date().toISOString(),
    count: packages.length,
    data: packages
  }
}

async function exportAll() {
  const [blogs, services, packages, testimonials, gallery, settings] = await Promise.all([
    exportBlogs(),
    exportServices(),
    exportPackages(),
    exportTestimonials(),
    exportGallery(),
    exportSettings()
  ])

  return {
    type: 'full_backup',
    exportDate: new Date().toISOString(),
    version: '1.0',
    blogs,
    services,
    packages,
    testimonials,
    gallery,
    settings
  }
}
