import { NextRequest, NextResponse } from 'next/server'
import { v2 as cloudinary } from 'cloudinary'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

export async function POST(request: NextRequest) {
  try {
    // Check if required environment variables are set
    if (!process.env.CLOUDINARY_API_SECRET) {
      return NextResponse.json(
        { error: 'CLOUDINARY_API_SECRET not configured' },
        { status: 500 }
      )
    }

    if (!process.env.CLOUDINARY_API_KEY) {
      return NextResponse.json(
        { error: 'CLOUDINARY_API_KEY not configured' },
        { status: 500 }
      )
    }

    const body = await request.json()
    const { folder = 'anjali-cms' } = body

    // Generate timestamp
    const timestamp = Math.round(new Date().getTime() / 1000)

    // Minimal parameters for signature (only essential ones)
    const params = {
      timestamp,
      folder,
    }

    // Generate signature
    const signature = cloudinary.utils.api_sign_request(
      params,
      process.env.CLOUDINARY_API_SECRET
    )

    // Log for debugging (remove in production)
    console.log('Signature params:', params)
    console.log('Generated signature:', signature)

    return NextResponse.json({
      signature,
      timestamp,
      api_key: process.env.CLOUDINARY_API_KEY,
      cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      folder,
      // Include params for debugging
      params,
    })
  } catch (error) {
    console.error('Error generating signature:', error)
    return NextResponse.json(
      { error: 'Failed to generate signature', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
