import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug } from '@/lib/utils'
import { z } from 'zod'
import { corsResponse, corsOptionsResponse } from '@/lib/cors'

const packageSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  services: z.array(z.string()).default([]),
  features: z.array(z.string()).default([]),
  price: z.string().min(1, 'Price is required'),
  originalPrice: z.string().optional(),
  duration: z.string().optional(),
  popular: z.boolean().default(false),
  image: z.string().optional(),
  category: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'ARCHIVED']).default('ACTIVE'),
})

export async function OPTIONS() {
  return corsOptionsResponse()
}

export async function GET(request: NextRequest) {
  try {
    // Public endpoint - no authentication required

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''
    const category = searchParams.get('category') || ''

    const skip = (page - 1) * limit

    const where: {
      OR?: Array<{
        name?: { contains: string; mode: 'insensitive' }
        description?: { contains: string; mode: 'insensitive' }
        category?: { contains: string; mode: 'insensitive' }
      }>
      status?: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
      popular?: boolean
      category?: { contains: string; mode: 'insensitive' }
    } = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { category: { contains: search, mode: 'insensitive' } },
      ]
    }
    
    if (status && (status === 'ACTIVE' || status === 'INACTIVE' || status === 'ARCHIVED')) {
      where.status = status
    }
    
    if (category) {
      where.category = { contains: category, mode: 'insensitive' }
    }

    const [packages, total] = await Promise.all([
      prisma.package.findMany({
        where,
        orderBy: [
          { popular: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      prisma.package.count({ where }),
    ])

    return corsResponse({
      packages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching packages:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = packageSchema.parse(body)

    const slug = generateSlug(validatedData.name)
    
    // Check if slug already exists
    const existingPackage = await prisma.package.findUnique({
      where: { slug },
    })
    
    if (existingPackage) {
      return NextResponse.json({ error: 'A package with this name already exists' }, { status: 400 })
    }

    const packageData = await prisma.package.create({
      data: {
        ...validatedData,
        slug,
      },
    })

    return NextResponse.json(packageData, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues }, { status: 400 })
    }
    
    console.error('Error creating package:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
