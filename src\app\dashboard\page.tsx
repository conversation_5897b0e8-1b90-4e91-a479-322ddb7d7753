"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText, Briefcase, MessageSquare, Images } from "lucide-react"

interface DashboardStats {
  blogs: number
  services: number
  testimonials: number
  gallery: number
}

interface RecentActivity {
  id: string
  title: string
  type: 'blog' | 'service' | 'testimonial' | 'gallery'
  createdAt: string
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    blogs: 0,
    services: 0,
    testimonials: 0,
    gallery: 0
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [blogsRes, servicesRes, testimonialsRes, galleryRes] = await Promise.all([
          fetch('/api/blogs'),
          fetch('/api/services'),
          fetch('/api/testimonials'),
          fetch('/api/gallery')
        ])

        const [blogs, services, testimonials, gallery] = await Promise.all([
          blogsRes.json(),
          servicesRes.json(),
          testimonialsRes.json(),
          galleryRes.json()
        ])

        setStats({
          blogs: blogs.length || 0,
          services: services.length || 0,
          testimonials: testimonials.filter((t: any) => t.status === 'APPROVED').length || 0,
          gallery: gallery.length || 0
        })

        // Combine recent items from all sources
        const recent: RecentActivity[] = [
          ...blogs.slice(0, 3).map((item: any) => ({
            id: item.id,
            title: item.title,
            type: 'blog' as const,
            createdAt: item.createdAt
          })),
          ...services.slice(0, 2).map((item: any) => ({
            id: item.id,
            title: item.title,
            type: 'service' as const,
            createdAt: item.createdAt
          })),
          ...gallery.slice(0, 2).map((item: any) => ({
            id: item.id,
            title: item.title,
            type: 'gallery' as const,
            createdAt: item.createdAt
          }))
        ]

        // Sort by creation date and take the 5 most recent
        recent.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        setRecentActivity(recent.slice(0, 5))
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to your content management system
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blog Posts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.blogs}
            </div>
            <p className="text-xs text-muted-foreground">
              Total blog posts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Services</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.services}
            </div>
            <p className="text-xs text-muted-foreground">
              Active services
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Testimonials</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.testimonials}
            </div>
            <p className="text-xs text-muted-foreground">
              Approved testimonials
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gallery Images</CardTitle>
            <Images className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.gallery}
            </div>
            <p className="text-xs text-muted-foreground">
              Total images
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest changes to your content
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p className="text-sm text-muted-foreground">Loading...</p>
            ) : recentActivity.length === 0 ? (
              <p className="text-sm text-muted-foreground">No recent activity</p>
            ) : (
              <div className="space-y-3">
                {recentActivity.map((item) => (
                  <div key={item.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{item.title}</p>
                      <p className="text-xs text-muted-foreground capitalize">
                        {item.type} • {new Date(item.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks you might want to perform
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <p className="text-sm text-muted-foreground">
              • Create a new blog post
            </p>
            <p className="text-sm text-muted-foreground">
              • Add a new service
            </p>
            <p className="text-sm text-muted-foreground">
              • Upload gallery images
            </p>
            <p className="text-sm text-muted-foreground">
              • Review testimonials
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
