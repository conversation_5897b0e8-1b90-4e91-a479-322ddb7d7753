# Anjali CMS API Documentation

This document outlines all the available API endpoints in the Anjali CMS system.

## Authentication

All API endpoints require authentication via NextAuth.js session. Include the session cookie in your requests.

## Base URL

```
http://localhost:3000/api
```

## Endpoints

### Authentication
- `GET/POST /api/auth/[...nextauth]` - NextAuth.js authentication endpoints

### Blogs
- `GET /api/blogs` - Get all blog posts with pagination and filtering
- `POST /api/blogs` - Create a new blog post
- `GET /api/blogs/[id]` - Get a specific blog post
- `PUT /api/blogs/[id]` - Update a blog post
- `DELETE /api/blogs/[id]` - Delete a blog post

### Categories
- `GET /api/categories` - Get all categories
- `POST /api/categories` - Create a new category

### Tags
- `GET /api/tags` - Get all tags
- `POST /api/tags` - Create a new tag

### Services
- `GET /api/services` - Get all services with pagination and filtering
- `POST /api/services` - Create a new service
- `GET /api/services/[id]` - Get a specific service
- `PUT /api/services/[id]` - Update a service
- `DELETE /api/services/[id]` - Delete a service

### Testimonials
- `GET /api/testimonials` - Get all testimonials with pagination and filtering
- `POST /api/testimonials` - Create a new testimonial
- `GET /api/testimonials/[id]` - Get a specific testimonial
- `PUT /api/testimonials/[id]` - Update a testimonial
- `DELETE /api/testimonials/[id]` - Delete a testimonial
- `POST /api/testimonials/bulk` - Bulk operations (approve, reject, delete)

### Gallery
- `GET /api/gallery` - Get all gallery items with pagination and filtering
- `POST /api/gallery` - Create a new gallery item
- `GET /api/gallery/[id]` - Get a specific gallery item
- `PUT /api/gallery/[id]` - Update a gallery item
- `DELETE /api/gallery/[id]` - Delete a gallery item
- `POST /api/gallery/bulk` - Bulk operations (feature, activate, delete, etc.)

### Settings
- `GET /api/settings` - Get all site settings
- `POST /api/settings` - Create a new setting
- `PUT /api/settings` - Update settings (supports bulk update)
- `GET /api/settings/[key]` - Get a specific setting
- `PUT /api/settings/[key]` - Update a specific setting
- `DELETE /api/settings/[key]` - Delete a setting

### File Upload
- `POST /api/upload` - Upload images to Cloudinary

## Query Parameters

### Pagination
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)

### Filtering
- `search` - Search term
- `status` - Filter by status
- `category` - Filter by category
- `featured` - Filter featured items (true/false)

### Example Requests

#### Get Blog Posts
```
GET /api/blogs?page=1&limit=10&search=makeup&status=PUBLISHED
```

#### Create Blog Post
```
POST /api/blogs
Content-Type: application/json

{
  "title": "Summer Makeup Trends",
  "content": "<p>Content here...</p>",
  "author": "Anjali",
  "status": "PUBLISHED",
  "featured": true,
  "categoryId": "category-id",
  "tagIds": ["tag1", "tag2"]
}
```

#### Bulk Update Testimonials
```
POST /api/testimonials/bulk
Content-Type: application/json

{
  "ids": ["id1", "id2", "id3"],
  "action": "approve"
}
```

## Response Format

### Success Response
```json
{
  "data": {...},
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 50,
    "pages": 5
  }
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": [...] // For validation errors
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## Data Models

### Blog
- `id` - Unique identifier
- `title` - Blog title
- `slug` - URL slug
- `excerpt` - Short description
- `content` - HTML content
- `author` - Author name
- `status` - DRAFT | PUBLISHED | ARCHIVED
- `featured` - Boolean
- `image` - Featured image URL
- `categoryId` - Category reference
- `tags` - Array of tag relationships
- `metaTitle` - SEO title
- `metaDescription` - SEO description
- `keywords` - Array of keywords
- `publishedAt` - Publication date
- `createdAt` - Creation date
- `updatedAt` - Last update date

### Service
- `id` - Unique identifier
- `title` - Service title
- `slug` - URL slug
- `description` - Service description
- `features` - Array of features
- `duration` - Service duration
- `price` - Service price
- `image` - Service image URL
- `category` - Service category
- `popular` - Boolean
- `status` - ACTIVE | INACTIVE | ARCHIVED
- `createdAt` - Creation date
- `updatedAt` - Last update date

### Testimonial
- `id` - Unique identifier
- `name` - Client name
- `email` - Client email (optional)
- `message` - Testimonial message
- `rating` - Rating (1-5)
- `image` - Client photo URL (optional)
- `service` - Related service (optional)
- `status` - PENDING | APPROVED | REJECTED
- `createdAt` - Creation date
- `updatedAt` - Last update date

### Gallery
- `id` - Unique identifier
- `title` - Image title
- `description` - Image description (optional)
- `image` - Image URL
- `category` - Image category (optional)
- `tags` - Array of tags
- `featured` - Boolean
- `status` - ACTIVE | INACTIVE | ARCHIVED
- `createdAt` - Creation date
- `updatedAt` - Last update date

### Setting
- `id` - Unique identifier
- `key` - Setting key (unique)
- `value` - Setting value
- `type` - TEXT | JSON | BOOLEAN | NUMBER | URL | EMAIL
- `description` - Setting description (optional)
- `createdAt` - Creation date
- `updatedAt` - Last update date
